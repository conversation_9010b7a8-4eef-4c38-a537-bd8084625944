console.log('LOAD JTE-ADDON.JS');
//console.log("TRISTON ASK TEST 222~");

function scrollTo(){
	$('html, body').animate({
        scrollTop: $('#listing').offset().top - 150
    }, 500);
}

function scrollToDoctor(){
	$('html, body').animate({
        scrollTop: $('#doctors').offset().top - 150
    }, 500);
}

// Function to attach click events to appointment buttons for Batu Kawan
function attachAppointmentButtonEvents() {
    console.log('Attaching events to appointment buttons...');

    // Remove existing event listeners to prevent duplicates
    $(document).off('click', '.appointment-btn');

    // Use event delegation for dynamically loaded content
    $(document).on('click', '.appointment-btn', function(e) {
        console.log('Appointment button clicked!');

        // Check if this is a Batu Kawan appointment button
        const href = $(this).attr('href');
        const isBatuKawan = href && href.includes('batu-kawan');

        if (isBatuKawan) {
            // Get the doctor name from the data attribute
            const doctorName = $(this).attr('data-doctor');
            console.log('<PERSON><PERSON> appointment - Doctor name from button click:', doctorName);

            if (doctorName) {
                // Check if this is a hash link (same page) or full URL (different page)
                if (href.startsWith('#')) {
                    // This is on the Batu Kawan page itself - scroll to form section
                    console.log('Same page - scrolling to form section');
                    e.preventDefault();

                    // Scroll to form section
                    const formSection = document.querySelector('#batu-kawan-maa') ||
                                      document.querySelector('.batu-kawan-maa') ||
                                      document.querySelector('form') ||
                                      document.querySelector('.gform_wrapper');

                    if (formSection) {
                        formSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    } else {
                        // Fallback scroll
                        window.scrollTo({
                            top: document.body.scrollHeight * 0.6,
                            behavior: 'smooth'
                        });
                    }

                    // Preselect doctor after scrolling
                    setTimeout(function() {
                        if (window.preselectDoctor && typeof window.preselectDoctor === 'function') {
                            window.preselectDoctor(doctorName);
                        }
                    }, 500);

                } else {
                    // This is from doctor listing page - open new tab with doctor parameter
                    console.log('Different page - opening new tab');
                    e.preventDefault();
                    const newUrl = href + '?doctor=' + encodeURIComponent(doctorName);
                    console.log('Opening new tab with URL:', newUrl);
                    window.open(newUrl, '_blank');
                }
            }
        }
        // For non-Batu Kawan buttons, let them navigate normally
    });
}

// Function to fix appointment buttons after AJAX calls
function fixAppointmentButtonsAfterAjax() {
    console.log('Fixing appointment buttons after AJAX...');

    // Get current URL parameters
    var urlParams = new URLSearchParams(window.location.search);
    var hospitalFilter = urlParams.get('jhospital');

    // Find all doctor cards and check their status
    $('.jteHospitalListingContentCol').each(function() {
        var $doctorCard = $(this);
        var $buttonContainer = $doctorCard.find('.jteHospitalListingContentColTxtBtn');
        var $doctorTitle = $doctorCard.find('.jteHospitalListingContentColTitleInner h3');
        var doctorName = $doctorTitle.text().trim();
        var hasStatus2 = $doctorCard.attr('data-has-status-2') === '1';

        console.log('AJAX Fix - Doctor:', doctorName, 'Has status=2:', hasStatus2);

        // Only add/show appointment button if doctor has status=2
        if (hasStatus2) {
            // If no button container exists or it's empty, add appointment button
            if ($buttonContainer.length === 0 || $buttonContainer.find('a').length === 0) {
                console.log('Adding missing appointment button for:', doctorName);

                var appointmentDiv = '';
                if (hospitalFilter === 'Batu Kawan') {
                    // Only Batu Kawan gets doctor parameter in URL for preselection
                    appointmentDiv = `<a href="https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa" class="raven-button appointment-btn" data-doctor="${doctorName}" title="Doctor: ${doctorName}">Make An Appointment</a>`;
                } else {
                    // Other hospitals get standard appointment button without data-doctor
                    appointmentDiv = `<a href="https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment" target="_blank">Make An Appointment</a>`;
                }

                if ($buttonContainer.length === 0) {
                    // Create button container if it doesn't exist
                    $doctorCard.find('.jteHospitalListingContentColInner').append(`<div class="jteHospitalListingContentColTxtBtn">${appointmentDiv}</div>`);
                } else {
                    // Add button to existing container
                    $buttonContainer.html(appointmentDiv);
                }
            } else {
                // Button exists, make sure it's visible
                $buttonContainer.show();
            }
        } else {
            // Doctor doesn't have status=2, hide the button
            if ($buttonContainer.length > 0) {
                $buttonContainer.hide();
                console.log('Hiding appointment button for:', doctorName, '(no status=2)');
            }
        }
    });

}

(function($) {
    $(function() {

    	$('#hospital-list').change(function() {
    		console.log($(this).val());
    	});

        console.log("jtest doctor script");
        console.log($("#hospital-list").val());

        // Initialize appointment button events on page load
        attachAppointmentButtonEvents();

        // Doctor Listing
	    if($(".jteDoctorListingContentContainer")[0]){
	    	console.log('jteDoctorListingContentContainer EXIST!!');

	    	let currentPage = 1;

			function updateURL() {
			    var value1 = $('#jhospital-list').val();
			    var value2 = $("#jspecialty-list").val();
			    var jsearchBox = $("#jsearchBox").val();
			    var searchValue = jsearchBox.replace(/’/g, "'");

			    var baseUrl = window.location.href.split('?')[0];
			    var params = [];

			    if (searchValue) {
			      	params.push('jsearch=' + searchValue);
			    }
			    if (value1 != 'all') {
			      	params.push('jhospital=' + value1);
			    } else {
			    	params.push('jhospital=' + '');
			    }
			    if (value2 != 'all') {
			      	params.push('jspecialty=' + value2);
			    } else {
			    	params.push('jspecialty=' + '');
			    }
			    

			    var newUrl = baseUrl + (params.length > 0 ? '?' + params.join('&') : '');

			    // Redirect to the new URL
			    window.history.pushState({}, '', newUrl);
			    // window.location.href = newUrl;
			}

			function updateURL2(paramName, paramValue) {
			    var baseUrl = window.location.href.split('?')[0];
			    var currentParams = new URLSearchParams(window.location.search);
			    
				// Set the default value for 'all' if paramValue is empty or equals 'all' - yosha
				if (paramValue === '' || paramValue === 'all') {
					currentParams.delete(paramName);  // Remove the parameter from the URL if reset is intended
				} else {
					currentParams.set(paramName, paramValue);  // Otherwise, set the parameter value
				}
				
				// if(paramValue == 'all'){
			    // 	paramValue = 'all';
			    // } else {
			    // 	paramValue = paramValue;
			    // }
			    if(paramName == "paged"){
			    	currentParams.set(paramName, paramValue);
			    }else{
			    	currentParams.set(paramName, paramValue);
			    	currentParams.set("paged", 1);
			    }
			    

			    var newUrl = baseUrl + '?' + currentParams.toString();

			    // Update the URL with pushState
			    history.pushState(null, '', newUrl);
			}


			function triggerAjax(ajaxpage) {
				function getUrlParameter(name) {
				  	var search = window.location.search.substring(1);
				  	var params = search.split('&');

				  	for (var i = 0; i < params.length; i++) {
				    	var param = params[i].split('=');
				    	var paramName = decodeURIComponent(param[0]);

				    	if (paramName === name) {
				      		return decodeURIComponent(param[1]);
				    	}
				  	}
				  	return null; // Return null if the parameter is not found
				}

				var searchParam0 = getUrlParameter('jsearch');
				var topicParam0 = getUrlParameter('jhospital');
				var specialtiesParam0 = getUrlParameter('jspecialty');
				
				if(searchParam0 != null){
					searchParam0 = searchParam0;
				} else {
					searchParam0 = '';
				}
				if(topicParam0 != null){
					topicParam0 = topicParam0.replace(/\+/g, ' ');
					//topicParam0 = topicParam0;
				} else {
					topicParam0 = 'all';
				}
				if(specialtiesParam0 != null){
					specialtiesParam0 = specialtiesParam0;
				} else {
					specialtiesParam0 = 'all';
				}
				console.log('searchParam0: ', searchParam0);
				console.log('topicParam0: ', topicParam0);
				console.log('specialtiesParam0: ', specialtiesParam0);

				var page = 1; // Initialize the current page
				var perPage = 9; // Number of posts per page
				if(ajaxpage != ""){
					page = ajaxpage;
				}
	            $.ajax({
		            url: '/malaysia/wp-admin/admin-ajax.php', // Use the URL to your WordPress admin-ajax.php file
		            type: 'POST',
		            data: {
		                action: 'custom_ajax_doctor_callback',
		                search: searchParam0,
		                hospital: topicParam0,
		                specialty: specialtiesParam0,
		                paged: page,
		                posts_per_page: $(".jteHAlistingPagination").data("ppp"),
		                // posts_per_page: perPage,
		            },
		            success: function(response) {	            	
		                console.log('response from AJAX custom_ajax_doctor_callback: ', response);
		                // console.log('response[0]: ', response[0]);
		                console.log('response.data.length: ', response.data.length)
		                console.log("test111");
		                $('.jteHospitalListingContent').children().remove();
		                var linkElement = $(response.pagination);
		                $(".jteHAlistingPagination").html('');
		                console.log($(".jteHAlistingPagination").data('page'));
		                $(".jteHAlistingPagination").data('page', response.paged);
		                console.log("test222");

		                if(response.paged < response.totalpage) {
		                	console.log('Not LAST PAGE for LM');
		                	currentPage = 1;
		                	$('.jteHAlistingPaginationLM').html('');
		                	$('.jteHAlistingPaginationLM').append('<a href="javascript:void(0)">Load More</a>');
		                } else {
		                	console.log('LAST PAGE for LM!');
		                	$('.jteHAlistingPaginationLM').html('');
		                }

		                if(response.data.length > 0){
		                	console.log('response != ');
		                	for (let i = 0; i < response.data.length; i++) {

		                		// Debug: Log the data structure
		                		console.log('Doctor data:', response.data[i]);
		                		console.log('Status:', response.data[i].status);
		                		console.log('Appointment URL:', response.data[i].appointmenturl);

		                		var appointmentDiv = '';

		                		// Check if doctor has status=2 appointments (from server response)
		                		var hasStatus2 = false;
		                		if (response.data[i].appointmenturl && response.data[i].appointmenturl.trim() !== '') {
		                			hasStatus2 = true; // If server provided appointment URL, doctor has status=2
		                		}

		                		// Only create appointment button if doctor has status=2
		                		if (hasStatus2) {
		                			// Get current URL parameters to check for hospital filter
		                			var urlParams = new URLSearchParams(window.location.search);
		                			var hospitalFilter = urlParams.get('jhospital');

		                			// Check if we have appointment URL from server
		                		if (response.data[i].appointmenturl.indexOf("|||") !== -1) {
		                			appointmentDiv = `<a href="javascript:void(0)" data-href="${response.data[i].appointmenturl}" class="doctorPopup">Make An Appointment</a>`;
		                		} else {
		                				// Check if this is Batu Kawan hospital or filter
		                				var appointmentUrl = response.data[i].appointmenturl;
		                				var linkClass = 'target="_blank"';
		                				var dataAttributes = '';

		                				if (hospitalFilter === 'Batu Kawan' || response.data[i].hospitalname === 'Batu Kawan') {
		                					appointmentUrl = 'https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa';
		                					linkClass = 'class="raven-button appointment-btn"';
		                					dataAttributes = `data-doctor="${response.data[i].post_title}" title="Doctor: ${response.data[i].post_title}"`;
		                				}

		                				appointmentDiv = `<a href="${appointmentUrl}" ${linkClass} ${dataAttributes}>Make An Appointment</a>`;
		                			}
		                		} else {
		                			// Doctor doesn't have status=2, no appointment button
		                			console.log('No appointment button for:', response.data[i].post_title, '(no status=2)');
		                		}

		                		var hospitalOutput = '';
		                		if (response.data[i].locationArray){
		                			$.each(response.data[i].locationArray, function(index, item) {
								        // Concatenate each anchor tag to the htmlString
								        // hospitalOutput += '<a href="' + item.url + '">' + item.title + '</a>';
								        
								        // NEW CODE
								        if (index !== response.data[i].locationArray.length - 1) {
								            hospitalOutput += '<a href="' + item.url + '">' + item.title + ',</a> ';
								        } else {
								            // If it's the last item, do not include the comma
								            hospitalOutput += '<a href="' + item.url + '">' + item.title + '</a>';
								        }

								    });
		                		}

		                		// Doctor Name Change Case
		                		function customTitleCase(string) {
					                // Words that should be fully uppercase
					                const uppercaseWords = ['A/P', 'A/L'];

					                // Split the string into words
					                let words = string.split(' ');

					                // Iterate through each word and convert accordingly
					                words = words.map(word => {
					                    if (uppercaseWords.includes(word.toUpperCase())) {
					                        return word.toUpperCase();
					                    } else {
					                        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
					                    }
					                });

					                // Join the words back into a single string
					                return words.join(' ');
					            }

		                		// console.log('appointmentDiv: ', appointmentDiv);
		                		
		                		var divhtml = '';

								// divhtml = `<div class="jteHospitalListingContentCol">
								// 	          	<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColImg" style="background-image: url(${response.data[i].post_image})"></a>
								// 	          	<div class="jteHospitalListingContentColInner">
								// 	          		<div class="jteHospitalListingContentColTitle">
								// 	          			<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColTitleInner">
								// 	          				<h3>${response.data[i].post_title}</h3>
								// 	          			</a>
								// 	          		</div>
								// 	          		<div class="jteHospitalListingContentColTxt">
								// 	          			<div class="jteHospitalListingContentColTxtList">
								// 		          			<p>${response.data[i].specialty}</p>
								// 		          		</div>
								// 	          			<div class="jteHospitalListingContentColTxtList2">
								// 		          			<p>${response.data[i].designation}</p>
								// 		          		</div>
								// 	          		</div>

								// 	          		<div class="jteHospitalListingContentColTxtBtn">
								// 	          			<a href="${response.data[i].appointmenturl}">Make An Appointment</a>
								// 	          		</div>
								// 	          	</div>
								// 	        </div>`;

								// Only include appointment button div if there's an appointment button
								var appointmentButtonDiv = appointmentDiv ? `<div class="jteHospitalListingContentColTxtBtn">${appointmentDiv}</div>` : '';

								// Determine if doctor has status=2 based on whether appointment button was created
								var hasStatus2 = appointmentDiv ? '1' : '0';

								divhtml = `<div class="jteHospitalListingContentCol ${response.data[i].doctor_class}" data-has-status-2="${hasStatus2}">
									          	<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColImg" style="background-image: url(${response.data[i].post_image})"></a>
									          	<div class="jteHospitalListingContentColInner">
									          		<div class="jteHospitalListingContentColTitle">
									          			<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColTitleInner">
									          				<h3>${customTitleCase(response.data[i].post_title)}</h3>
									          			</a>
									          		</div>
									          		<div class="jteHospitalListingContentColTxt">
									          			<div class="jteHospitalListingContentColTxtList">
										          			<p>${response.data[i].specialty}</p>
										          		</div>
									          			<div class="jteHospitalListingContentColTxtList2">
										          			<p>${response.data[i].designation}</p>
										          		</div>

										          		<div class="jteDoctorCarouselListHospital">
										          			<i></i>
										          			<div>
										          				${hospitalOutput}
										          			</div>
										          		</div>
									          		</div>
									          		${appointmentButtonDiv}
									          	</div>
									        </div>`;

		                		$('.jteHospitalListingContent').append(divhtml);
		                		$(".jteHAlistingPagination").data('maxpage', response.totalpage);
		                		// console.log('TOTAL PAGE: ', response.totalpage);
							}
		                } else {
		                	var noPost = "Sorry, no doctors found. Perhaps you'd like to explore other hospitals/specialties?";
		                	console.log('response == ');
		                	$('.jteHospitalListingContent').append('<div class="jteHospitalListingContentNoPost"><p>'+noPost+'</p></div>');
		                }
		                console.log("test333");
		                console.log(linkElement[1], Object.keys(linkElement).length);
		                if(Object.keys(linkElement).length > 3){
		                	console.log('NOT a.next', linkElement);
		                	$(".jteHAlistingPagination").html(linkElement);
		                	$(".jteHAlistingPagination").append('<div class="jteHAlistingPaginationNumber">Go to <input type="number" value="'+response.paged+'" min="1"></div>');
		                } else {
		                	console.log('IS a.next');
		                }
		                // $(".jteHAlistingPagination").html(linkElement);
		                console.log("test444");
		                
		                // Fix any missing appointment buttons and reattach events
		                setTimeout(function() {
		                    fixAppointmentButtonsAfterAjax();
		                    attachAppointmentButtonEvents();
		                }, 100);

		            },
		            error: function(XMLHttpRequest, textStatus, errorThrown) { 
	                    console.log("Status: " + textStatus); 
	                    console.log("Error: " + errorThrown); 
	                    console.log(XMLHttpRequest);
	                } 

		        });
			}


			$("#jhospital-list").selectmenu();
			// $("#jteHAlistingFilterTopics").val('square');
			$('#jhospital-list').on('selectmenuchange', function(event, ui) {
			    console.log( 'jhospital-list Selected', $(this).val()); 

				updateURL2('jhospital', $(this).val());

				triggerAjax(1);
			});

			$("#jspecialty-list").selectmenu();
			$('#jspecialty-list').on('selectmenuchange', function(event, ui) {
			    console.log( 'jspecialty-list Selected', $(this).val()); 

			    // updateURL();
			    updateURL2('jspecialty', $(this).val());

			    triggerAjax(1);
			});

			$(".jteDoctorListingContentContainer .jteHAlistingPagination ").on('keypress', '.jteHAlistingPaginationNumber input[type="number"]', function(e) {
		    	var pageNumber = $(this).val();
		        var maxPages = parseInt($('.jteHAlistingPagination').data('maxpage'), 10); 

		        pageNumber = Math.min(pageNumber, maxPages);

		        console.log('pageNumber: ', pageNumber, maxPages);

		        if (e.which === 13) { // Enter key
		            e.preventDefault();
		            updateURL2('paged', pageNumber);
					triggerAjax(pageNumber);
		        }

		        scrollToDoctor();
	    	})

			$(".jteDoctorListingContentContainer .jteHAlistingPagination ").on("click", 'a' , function (event) { 
                event.preventDefault();
                console.log($(this).html());
                var currentpage = 1;
                var requestpage = 0;
                var maxpage = 0;
                if($(this).html() == "Previous"){
                	currentpage = $(this).parent().data("page");
                	requestpage = currentpage - 1;
                	if(currentpage > 1){
                		updateURL2('paged', requestpage);
                		triggerAjax(requestpage);
                	}
                }else if($(this).html() == "Next"){
                	
                	currentpage = $(this).parent().data("page");
                	maxpage = $(this).parent().data("maxpage");
                	requestpage = currentpage + 1;
                	console.log('NEXT!', currentpage, maxpage, requestpage);
                	if(currentpage > 0 && requestpage <= maxpage){
                		updateURL2('paged', requestpage);
                		triggerAjax(requestpage);
                	}
                }else{
                	requestpage = parseInt($(this).html());
                	updateURL2('paged', requestpage);
                	triggerAjax(requestpage);
                }

                scrollToDoctor();
            }); 


			checkInput();
			// $("#jsearchBox").keypress(function(event){
			$("#jsearchBox").on('keypress input', function() {
		        var keycode = (event.keyCode ? event.keyCode : event.which);
		        const url = new URL(window.location.href);
		        // console.log('url:', url);
		        const cleanURL = url.href.replace(/\/page\/\d+/, '');
		        // console.log('cleanURL:', cleanURL);
		        url.href = cleanURL;
		        // console.log('url2:', url);

		        checkInput();

		        if(keycode == '13'){
		          	console.log('You pressed a "enter" key in textbox', $(this).val()); 

					updateURL2('jsearch', $(this).val());

					triggerAjax(1);
					
		        }
		        //Stop the event from propogation to other handlers
		        //If this line will be removed, then keypress event handler attached 
		        //at document level will also be triggered
		        event.stopPropagation();
		    });

		    $(document).keypress(function(event){
		        var keycode = (event.keyCode ? event.keyCode : event.which);
		        if(keycode == '13'){
		          console.log('You pressed a "enter" key in somewhere');  
		        }
		    });

		    function checkInput() {
			    if ($("#jsearchBox").val().trim() === '') {
			      	$('.CAHclearBtn').hide();
			    } else {
			      	$('.CAHclearBtn').show();
			    }
			}
			$('.CAHclearBtn').on("click", function (event) { 
            	event.preventDefault();
            	$("#jsearchBox").val('');
            	$(this).hide();
            	console.log('CLEAR');

            	// var enterEvent = jQuery.Event("keypress");
		        // enterEvent.keyCode = 13; // Set the Enter key code
		        // $('#jsearchBox').focus();
		        // $("#jsearchBox").trigger(enterEvent);

		        // updateURL2('jsearch', $("#jsearchBox").val());
				updateURL2('jsearch', ''); // Pass empty string to remove the search parameter -yosha

				triggerAjax(1);
        	});


		    $('body').on('click', '.jteHAlistingPaginationLM a', function(){
        	
	        	currentPage++;
	        	var postsPage = 9;
	        	console.log('Load More CLICKED!', postsPage);

	        	function getUrlParameter(name) {
				  	var search = window.location.search.substring(1);
				  	var params = search.split('&');

				  	for (var i = 0; i < params.length; i++) {
				    	var param = params[i].split('=');
				    	var paramName = decodeURIComponent(param[0]);

				    	if (paramName === name) {
				      		return decodeURIComponent(param[1]);
				    	}
				  	}
				  	return null; // Return null if the parameter is not found
				}

	        	var searchParam0 = getUrlParameter('jsearch');
				var topicParam0 = getUrlParameter('jhospital');
				var specialtiesParam0 = getUrlParameter('jspecialty');

				console.log('topicParam1: ', topicParam0);

				if(searchParam0 != null){
					searchParam0 = searchParam0;
				} else {
					searchParam0 = '';
				}
				if(topicParam0 != null){
					topicParam0 = topicParam0.replace(/\+/g, ' ');
					//topicParam0 = topicParam0;
				} else {
					topicParam0 = 'all';
				}
				if(specialtiesParam0 != null){
					specialtiesParam0 = specialtiesParam0;
				} else {
					specialtiesParam0 = 'all';
				}
				
				console.log('searchParam0: ', searchParam0);
				console.log('topicParam0: ', topicParam0);
				console.log('specialtiesParam0: ', specialtiesParam0);

				console.log('currentPage: ', currentPage);
				console.log('postsPage: ', postsPage);
				

				// var page = 1; // Initialize the current page

		        var button = $(this);
		        	// ,
		            // data = {
		            //     'action': 'loadmore',
		            //     'query': loadmore_params.posts,
		            //     'page' : loadmore_params.current_page
		            // };

		        $.ajax({
		            url: '/malaysia/wp-admin/admin-ajax.php', // Use the URL to your WordPress admin-ajax.php file
		            type: 'POST',
		            data: {
		                action: 'custom_ajax_doctor_callback',
		                search: searchParam0,
		                hospital: topicParam0,
		                specialty: specialtiesParam0,
		                paged: currentPage,
		                posts_per_page: postsPage,
		                // posts_per_page: $(".jteHAlistingPaginationLM").data("ppp"),
		            },
		            beforeSend : function(xhr){
		                button.text('Loading...');
		            },
		            success : function(response){
		                // if(response) {
		                //     button.text('Load More').before(response);
		                //     loadmore_params.current_page++;
		                // } else {
		                //     button.remove();
		                // }

		                console.log('response from AJAX Hospital Listing: ', response);
		                // console.log('response[0]: ', response[0]);
		                console.log('response.length: ', response.data.length);

		                var linkElement = $(response.pagination);
		                console.log('linkElement: ', linkElement);
		                // $(".jteHAlistingPagination").html('');
		                $(".jteHAlistingPaginationLM").data('page', response.paged);

		                // $('.jteHAlistingContent').children().remove();

		                if(response.data.length < postsPage){
		                	button.remove();
		                } else {
		                	button.text('Load More');
		                }

		                // if(response.data != ''){
		                if(response.data.length > 0){
		                	console.log('response != ');
		                	for (let i = 0; i < response.data.length; i++) {

		                		// var divhtml = '';

								// divhtml = `<div class="jteHospitalListingContentCol ${response.data[i].doctor_class}">
							    //       	<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColImg" style="background-image: url(${response.data[i].post_image})"></a>
							    //       	<div class="jteHospitalListingContentColInner">
							    //       		<div class="jteHospitalListingContentColTitle">
							    //       			<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColTitleInner">
							    //       				<h3>${response.data[i].post_title}</h3>
							    //       			</a>
							    //       		</div>
							    //       		<div class="jteHospitalListingContentColTxt">
							    //       			<div class="jteHospitalListingContentColTxtList">
								//           			<p>${response.data[i].specialty}</p>
								//           		</div>
							    //       			<div class="jteHospitalListingContentColTxtList2">
								//           			<p>${response.data[i].designation}</p>
								//           		</div>
							    //       		</div>

							    //       		<div class="jteHospitalListingContentColTxtBtn">
							    //       			<a href="#" data-href="${response.data[i].appointmenturl}">Make An Appointment</a>
							    //       		</div>
							    //       	</div>
							    //     </div>`;

		                		// Debug: Log the data structure
		                		console.log('Load More Doctor data:', response.data[i]);
		                		console.log('Load More Status:', response.data[i].status);
		                		console.log('Load More Appointment URL:', response.data[i].appointmenturl);

		                		var appointmentDiv = '';

		                		// Check if doctor has status=2 appointments (from server response)
		                		var hasStatus2 = false;
		                		if (response.data[i].appointmenturl && response.data[i].appointmenturl.trim() !== '') {
		                			hasStatus2 = true; // If server provided appointment URL, doctor has status=2
		                		}

		                		// Only create appointment button if doctor has status=2
		                		if (hasStatus2) {
		                			// Get current URL parameters to check for hospital filter
		                			var urlParams = new URLSearchParams(window.location.search);
		                			var hospitalFilter = urlParams.get('jhospital');

		                			// Check if we have appointment URL from server
		                		if (response.data[i].appointmenturl.indexOf("|||") !== -1) {
		                			appointmentDiv = `<a href="javascript:void(0)" data-href="${response.data[i].appointmenturl}" class="doctorPopup">Make An Appointment</a>`;
		                		} else {
		                				// Check if this is Batu Kawan hospital or filter
		                				var appointmentUrl = response.data[i].appointmenturl;
		                				var linkClass = 'target="_blank"';
		                				var dataAttributes = '';

		                				if (hospitalFilter === 'Batu Kawan' || response.data[i].hospitalname === 'Batu Kawan') {
		                					appointmentUrl = 'https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa';
		                					linkClass = 'class="raven-button appointment-btn"';
		                					dataAttributes = `data-doctor="${response.data[i].post_title}" title="Doctor: ${response.data[i].post_title}"`;
		                				}

		                				appointmentDiv = `<a href="${appointmentUrl}" ${linkClass} ${dataAttributes}>Make An Appointment</a>`;
		                			}
		                		} else {
		                			// Doctor doesn't have status=2, no appointment button
		                			console.log('Load More: No appointment button for:', response.data[i].post_title, '(no status=2)');
		                		}

		                		var hospitalOutput = '';
		                		if (response.data[i].locationArray){
		                			$.each(response.data[i].locationArray, function(index, item) {
								        // Concatenate each anchor tag to the htmlString
								        hospitalOutput += '<a href="' + item.url + '">' + item.title + '</a>';
								    });
		                		}
		                		
		                		var divhtml = '';

								// Only include appointment button div if there's an appointment button
								var appointmentButtonDiv = appointmentDiv ? `<div class="jteHospitalListingContentColTxtBtn">${appointmentDiv}</div>` : '';

								// Determine if doctor has status=2 based on whether appointment button was created
								var hasStatus2 = appointmentDiv ? '1' : '0';

								divhtml = `<div class="jteHospitalListingContentCol ${response.data[i].doctor_class}" data-has-status-2="${hasStatus2}">
									          	<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColImg" style="background-image: url(${response.data[i].post_image})"></a>
									          	<div class="jteHospitalListingContentColInner">
									          		<div class="jteHospitalListingContentColTitle">
									          			<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColTitleInner">
									          				<h3>${response.data[i].post_title}</h3>
									          			</a>
									          		</div>
									          		<div class="jteHospitalListingContentColTxt">
									          			<div class="jteHospitalListingContentColTxtList">
										          			<p>${response.data[i].specialty}</p>
										          		</div>
									          			<div class="jteHospitalListingContentColTxtList2">
										          			<p>${response.data[i].designation}</p>
										          		</div>

										          		<div class="jteDoctorCarouselListHospital">
										          			<i></i>
										          			<div>
										          				${hospitalOutput}
										          			</div>
										          		</div>
									          		</div>
									          		${appointmentButtonDiv}
									          	</div>
									        </div>`;

		                		$('.jteHospitalListingContent').append(divhtml);
							}

							
		                } else {
		                	console.log('response == ');
		                	$('.jteHAlistingContent').append('<div class="jteHospitalListingContentNoPost"><p>No posts found :(</p></div>');
		                	button.remove();
		                }

		                // Reattach events to new appointment buttons
		                setTimeout(function() {
		                    attachAppointmentButtonEvents();
		                }, 100);
		            }
		        });

		        
		    });


		    function getURLParameters() {
			  	var search = window.location.search.substring(1); // Get the query string without the '?'
			  	var params = search.split('&');
			  	var paramObj = {};

			  	for (var i = 0; i < params.length; i++) {
				    var keyValue = params[i].split('=');
				    var key = decodeURIComponent(keyValue[0]);
				    var value = decodeURIComponent(keyValue[1]);
				    paramObj[key] = value;
			  	}

			  	return paramObj;
			}

			var params = getURLParameters();
			var paramSearch = 'jsearch';
			var paramTopic = 'jhospital';
			var paramSpecialties = 'jspecialty';

	  		console.log('params: ', params);

	  		if(paramSearch != ''){
	  			console.log('params[paramSearch]: ', params[paramSearch]);
	  			$('#searchBox').val(params[paramSearch]);
	  		} else {
	  			$('#searchBox').val('');
	  		}

	  		if(paramTopic in params){
	  			console.log('params[paramTopic]: ', params[paramTopic]);
	  			if(params[paramTopic] != 'undefined'){
	  				console.log('Have Topic: ', paramTopic);
	  				$("#jhospital-list").val(params[paramTopic]).selectmenu("refresh");
	  			} else {
	  				console.log('Empty Topic');
	  				$("#jhospital-list").val('all').selectmenu("refresh");
	  			}
	  			
	  		} else {
	  			console.log('params[paramTopic] haha: ');
	  			$("#jhospital-list").val('all').selectmenu("refresh");
	  		}

	  		if(paramSpecialties in params){
	  			console.log('params[paramSpecialties]: ', params[paramSpecialties]);
	  			if(params[paramSpecialties] != 'undefined'){
	  				$("#jspecialty-list").val(params[paramSpecialties]).selectmenu("refresh");
	  			} else {
	  				$("#jspecialty-list").val('all').selectmenu("refresh");
	  			}
	  			
	  		} else {
	  			$("#jspecialty-list").val('all').selectmenu("refresh");
	  		}

	  		if(params) {
	  			// var offset = $('#doctors').offset().top - 120;
	  			// console.log('animate!!', $('#doctors').offset().top, offset);
	  			// $('html, body').animate({ scrollTop: offset }, 1000);

				// $(document).ready(function() {
			    //      $('html, body').animate({
			    //        'scrollTop':   $('#doctors').offset().top - 120
			    //      }, 1000);
			    // });
	  		}

	  		if (params[""] !== undefined) {
			    console.log('NOT undefined 02');
			} else {
				console.log('undefined 02');
				scrollToDoctor();
			}


	    };


	    // Specialty Listing
	    if($(".jteSpecialtyListingContentContainer")[0]){
	    	console.log('jteSpecialtyListingContentContainer EXIST!!');

	    	let currentPage = 1;

			function updateURL() {
			    var value1 = $('#jhospital-list').val();
			    var value2 = $("#jspecialty-list").val();
			    var searchValue = $("#jsearchBox").val();

			    var baseUrl = window.location.href.split('?')[0];
			    var params = [];

			    if (searchValue) {
			      	params.push('jsearch=' + searchValue);
			    }
			    if (value1 != 'all') {
			      	params.push('jhospital=' + value1);
			    } else {
			    	params.push('jhospital=' + '');
			    }
			    if (value2 != 'all') {
			      	params.push('jspecialty=' + value2);
			    } else {
			    	params.push('jspecialty=' + '');
			    }
			    

			    var newUrl = baseUrl + (params.length > 0 ? '?' + params.join('&') : '');

			    // Redirect to the new URL
			    window.history.pushState({}, '', newUrl);
			    // window.location.href = newUrl;
			}

			function updateURL2(paramName, paramValue) {
			    var baseUrl = window.location.href.split('?')[0];
			    var currentParams = new URLSearchParams(window.location.search);
			    if(paramValue == 'all'){
			    	paramValue = 'all';
			    } else {
			    	paramValue = paramValue;
			    }
			    if(paramName == "paged"){
			    	currentParams.set(paramName, paramValue);
			    }else{
			    	currentParams.set(paramName, paramValue);
			    	currentParams.set("paged", 1);
			    }
			    

			    var newUrl = baseUrl + '?' + currentParams.toString();

			    // Update the URL with pushState
			    history.pushState(null, '', newUrl);
			}


			function triggerAjax(ajaxpage) {
				function getUrlParameter(name) {
				  	var search = window.location.search.substring(1);
				  	var params = search.split('&');

				  	for (var i = 0; i < params.length; i++) {
				    	var param = params[i].split('=');
				    	var paramName = decodeURIComponent(param[0]);

				    	if (paramName === name) {
				      		return decodeURIComponent(param[1]);
				    	}
				  	}
				  	return null; // Return null if the parameter is not found
				}

				var searchParam0 = getUrlParameter('jsearch');
				var topicParam0 = getUrlParameter('jhospital');
				var specialtiesParam0 = getUrlParameter('jspecialty');
				
				if(searchParam0 != null){
					searchParam0 = searchParam0;
				} else {
					searchParam0 = '';
				}
				if(topicParam0 != null){
					topicParam0 = topicParam0;
				} else {
					topicParam0 = 'all';
				}
				if(specialtiesParam0 != null){
					specialtiesParam0 = specialtiesParam0;
				} else {
					specialtiesParam0 = 'all';
				}
				console.log('searchParam0: ', searchParam0);
				console.log('topicParam0: ', topicParam0);
				console.log('specialtiesParam0: ', specialtiesParam0);

				var page = 1; // Initialize the current page
				// var perPage = 9; // Number of posts per page
				if(ajaxpage != ""){
					page = ajaxpage;
				}
	            $.ajax({
		            url: '/malaysia/wp-admin/admin-ajax.php', // Use the URL to your WordPress admin-ajax.php file
		            type: 'POST',
		            data: {
		                action: 'custom_ajax_specialtylisting_callback',
		                search: searchParam0,
		                hospital: topicParam0,
		                specialty: specialtiesParam0,
		                paged: page,
		                // posts_per_page: $(".jteHAlistingPagination").data("ppp"),
		                posts_per_page: 9,
		            },
		            success: function(response) {	            	
		                console.log('response from AJAX custom_ajax_specialtylisting_callback: ', response);
		                // console.log('response[0]: ', response[0]);
		                console.log('response.data.length: ', response.data.length)
		                console.log("test111");
		                $('.jteHospitalListingContent').children().remove();
		                var linkElement = $(response.pagination);
		                $(".jteHAlistingPagination").html('');
		                console.log($(".jteHAlistingPagination").data('page'));
		                $(".jteHAlistingPagination").data('page', response.paged);
		                console.log("test222");

		                if(response.paged < response.totalpage) {
		                	console.log('Not LAST PAGE for LM');
		                	currentPage = 1;
		                	$('.jteHAlistingPaginationLM').html('');
		                	$('.jteHAlistingPaginationLM').append('<a href="javascript:void(0)">Load More</a>');
		                } else {
		                	console.log('LAST PAGE for LM!');
		                	$('.jteHAlistingPaginationLM').html('');
		                }

		                if(response.data.length > 0){
		                	console.log('response != ');
		                	for (let i = 0; i < response.data.length; i++) {

		                		
		                		var divhtml = '';

								// divhtml = `<div class="jteHospitalListingContentCol">
								// 	          	<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColImg" style="background-image: url(${response.data[i].post_image})"></a>
								// 	          	<div class="jteHospitalListingContentColInner">
								// 	          		<div class="jteHospitalListingContentColTitle">
								// 	          			<a href="${response.data[i].profileurl}" class="jteHospitalListingContentColTitleInner">
								// 	          				<h3>${response.data[i].post_title}</h3>
								// 	          			</a>
								// 	          		</div>
								// 	          		<div class="jteHospitalListingContentColTxt">
								// 	          			<div class="jteHospitalListingContentColTxtList">
								// 		          			<p></p>
								// 		          		</div>
								// 	          			<div class="jteHospitalListingContentColTxtList2">
								// 		          			<p>${response.data[i].post_excerpt}</p>
								// 		          		</div>
								// 	          		</div>
								// 	          	</div>
								// 	        </div>`;

								divhtml = `<a href="${response.data[i].profileurl}" class="jteHAlistingContentList jteHPlistingContentList">
							            	<div class="jteHPlistingContentListImg" style="background-image:url(${response.data[i].post_image})"></div>
							            	<div class="jteHPlistingContentListContent">
							            		<div class="jteHPlistingContentListTitle">
							            			<h3 class="jteHPlistingTitle">${response.data[i].post_title}</h3>
							            		</div>
							            		
							            		<div class="jteHPlistingContentListDesc">
							            			<p class="">${response.data[i].post_excerpt}</p>
							            		</div>
							            	</div>
							            </a>`;

		                		$('.jteHospitalListingContent').append(divhtml);
		                		$(".jteHAlistingPagination").data('maxpage', response.totalpage);
							}
		                } else {
		                	var noPost = "Sorry, no specialties found. Perhaps you'd like to explore other hospitals/specialties?";
		                	console.log('response == ');
		                	$('.jteHospitalListingContent').append('<div class="jteHospitalListingContentNoPost"><p>'+noPost+'</p></div>');
		                }
		                console.log("test333", linkElement);
		                if(Object.keys(linkElement).length > 3){
		                	// console.log('NOT a.next');
		                	$(".jteHAlistingPagination").html(linkElement);
		                	$(".jteHAlistingPagination").append('<div class="jteHAlistingPaginationNumber">Go to <input type="number" value="'+response.paged+'" min="1"></div>');
		                } else {
		                	// console.log('IS a.next');
		                }
		                // $(".jteHAlistingPagination").html(linkElement);
		                console.log("test444");
		                
		            },
		            error: function(XMLHttpRequest, textStatus, errorThrown) { 
	                    console.log("Status: " + textStatus); 
	                    console.log("Error: " + errorThrown); 
	                    console.log(XMLHttpRequest);
	                } 

		        });
			}


			$("#jhospital-list").selectmenu();
			// $("#jteHAlistingFilterTopics").val('square');
			$('#jhospital-list').on('selectmenuchange', function(event, ui) {
			    console.log( 'jhospital-list Selected', $(this).val()); 

				updateURL2('jhospital', $(this).val());

				triggerAjax(1);

				checkHospital();
			});

			$("#jspecialty-list").selectmenu();
			$('#jspecialty-list').on('selectmenuchange', function(event, ui) {
			    console.log( 'jspecialty-list Selected', $(this).val()); 

			    // updateURL();
			    updateURL2('jspecialty', $(this).val());

			    triggerAjax(1);
			});

			$(".jteHospitalListingContentContainer .jteHAlistingPagination ").on('keypress', '.jteHAlistingPaginationNumber input[type="number"]', function(e) {
		    	var pageNumber = $(this).val();
		        var maxPages = parseInt($('.jteHAlistingPagination').data('maxpage'), 10); 

		        pageNumber = Math.min(pageNumber, maxPages);

		        console.log('pageNumber: ', pageNumber, maxPages);

		        if (e.which === 13) { // Enter key
		            e.preventDefault();
		            updateURL2('paged', pageNumber);
					triggerAjax(pageNumber);
		        }

		        scrollTo();
	    	})

			$(".jteSpecialtyListingContentContainer .jteHAlistingPagination ").on("click", 'a' , function (event) { 
                event.preventDefault();
                console.log($(this).html());
                var currentpage = 1;
                var requestpage = 0;
                var maxpage = 0;
                if($(this).html() == "Previous"){
                	currentpage = $(this).parent().data("page");
                	requestpage = currentpage - 1;
                	if(currentpage > 1){
                		updateURL2('paged', requestpage);
                		triggerAjax(requestpage);
                	}
                }else if($(this).html() == "Next"){
                	currentpage = $(this).parent().data("page");
                	maxpage = $(this).parent().data("maxpage");
                	requestpage = currentpage + 1;
                	if(currentpage > 0 && requestpage <= maxpage){
                		updateURL2('paged', requestpage);
                		triggerAjax(requestpage);
                	}
                }else{
                	requestpage = parseInt($(this).html());
                	updateURL2('paged', requestpage);
                	triggerAjax(requestpage);
                }

                scrollTo();
            }); 


			checkInput();
			// $("#jsearchBox").keypress(function(event){
			$("#jsearchBox").on('keypress input', function() {
		        var keycode = (event.keyCode ? event.keyCode : event.which);
		        const url = new URL(window.location.href);
		        // console.log('url:', url);
		        const cleanURL = url.href.replace(/\/page\/\d+/, '');
		        // console.log('cleanURL:', cleanURL);
		        url.href = cleanURL;
		        // console.log('url2:', url);

		        // console.log($(this).val());

		        // if($(this).val() != ''){
		        // 	$(this).siblings('a').show();
		        // 	console.log('$(this).val() != ');
		        // } else {
		        // 	$(this).siblings('a').hide();
		        // 	console.log('$(this).val() == EMPTY ');
		        // }
		        // $(this).parent().on("click", 'a' , function (event) { 
                // 	event.preventDefault();
                // 	$(this).siblings('input').val('');
                // 	$(this).hide();
            	// });
            	checkInput();

		        if(keycode == '13'){
		          	console.log('You pressed a "enter" key in textbox', $(this).val()); 

					updateURL2('jsearch', $(this).val());

					triggerAjax(1);
					
		        }
		        //Stop the event from propogation to other handlers
		        //If this line will be removed, then keypress event handler attached 
		        //at document level will also be triggered
		        event.stopPropagation();
		    });

		    function checkInput() {
			    // if ($("#jsearchBox").val().trim() === '') {
			    //   	$('.CAHclearBtn').hide();
			    // } else {
			    //   	$('.CAHclearBtn').show();
			    // }
			}
			$('.CAHclearBtn').on("click", function (event) { 
            	event.preventDefault();
            	$("#jsearchBox").val('');
            	$(this).hide();
            	console.log('CLEAR');

    	        updateURL2('jsearch', $("#jsearchBox").val());
    			triggerAjax(1);
        	});

		    $(document).keypress(function(event){
		        var keycode = (event.keyCode ? event.keyCode : event.which);
		        if(keycode == '13'){
		          console.log('You pressed a "enter" key in somewhere');  
		        }
		    });


		    $('body').on('click', '.jteHAlistingPaginationLM a', function(){
        	
	        	currentPage++;
	        	var postsPage = 9;
	        	
	        	console.log('Load More CLICKED!');

	        	function getUrlParameter(name) {
				  	var search = window.location.search.substring(1);
				  	var params = search.split('&');

				  	for (var i = 0; i < params.length; i++) {
				    	var param = params[i].split('=');
				    	var paramName = decodeURIComponent(param[0]);

				    	if (paramName === name) {
				      		return decodeURIComponent(param[1]);
				    	}
				  	}
				  	return null; // Return null if the parameter is not found
				}

				var searchParam0 = getUrlParameter('jsearch');
				var topicParam0 = getUrlParameter('jhospital');
				var specialtiesParam0 = getUrlParameter('jspecialty');
				
				if(searchParam0 != null){
					searchParam0 = searchParam0;
				} else {
					searchParam0 = '';
				}
				if(topicParam0 != null){
					topicParam0 = topicParam0;
				} else {
					topicParam0 = 'all';
				}
				if(specialtiesParam0 != null){
					specialtiesParam0 = specialtiesParam0;
				} else {
					specialtiesParam0 = 'all';
				}
				console.log('searchParam0: ', searchParam0);
				console.log('topicParam0: ', topicParam0);
				console.log('specialtiesParam0: ', specialtiesParam0);

				console.log('currentPage: ', currentPage);
				console.log('postsPage: ', postsPage);
				

				// var page = 1; // Initialize the current page

		        var button = $(this);
		        	// ,
		            // data = {
		            //     'action': 'loadmore',
		            //     'query': loadmore_params.posts,
		            //     'page' : loadmore_params.current_page
		            // };

		        $.ajax({
		            url: '/malaysia/wp-admin/admin-ajax.php', // Use the URL to your WordPress admin-ajax.php file
		            type: 'POST',
		            data: {
		               	action: 'custom_ajax_specialtylisting_callback',
				        search: searchParam0,
				        hospital: topicParam0,
				        specialty: specialtiesParam0,
		                paged: currentPage,
		                posts_per_page: postsPage,
		                // posts_per_page: $(".jteHAlistingPaginationLM").data("ppp"),
		            },
		            beforeSend : function(xhr){
		                button.text('Loading...');
		            },
		            success : function(response){
		                // if(response) {
		                //     button.text('Load More').before(response);
		                //     loadmore_params.current_page++;
		                // } else {
		                //     button.remove();
		                // }

		                console.log('response from AJAX Health Article Listing LM: ', response);
		                // console.log('response[0]: ', response[0]);
		                console.log('response.length: ', response.data.length);

		                var linkElement = $(response.pagination);
		                console.log('linkElement: ', linkElement);
		                // $(".jteHAlistingPagination").html('');
		                $(".jteHAlistingPaginationLM").data('page', response.paged);

		                // $('.jteHAlistingContent').children().remove();

		                if(response.data.length < postsPage){
		                	button.remove();
		                } else {
		                	button.text('Load More');
		                }

		                // if(response.data != ''){
		                if(response.data.length > 0){
		                	console.log('response != ');
		                	for (let i = 0; i < response.data.length; i++) {

		                		// $('.jteHAlistingContent').append("<a href='javascript:void(0)' class='jteHAlistingContentList' data-yt='"+ response.data[i].embedLink +"'><div class='jteHAlistingContentListImg jteVideolistingContentImg' style='background-image:url("+ response.data[i].post_img +")'><div class='jteVideolistingContentBG'><i class='"+ response.data[i].types +"'></i></div></div><div class='jteHAlistingContentListContent'><div class='jteHAlistingContentListTitle'><h3 class='jteNewGridWrapListTitle'>"+ response.data[i].post_name +"</h3></div><div class='jteHAlistingContentListFooter'><p class='jteHAlistingContentListDate'>"+ response.data[i].post_date +"</p></div></div></a>");

		                		var divhtml = '';

								divhtml = `<a href="${response.data[i].profileurl}" class="jteHAlistingContentList jteHPlistingContentList">
							            	<div class="jteHPlistingContentListImg" style="background-image:url(${response.data[i].post_image})"></div>
							            	<div class="jteHPlistingContentListContent">
							            		<div class="jteHPlistingContentListTitle">
							            			<h3 class="jteHPlistingTitle">${response.data[i].post_title}</h3>
							            		</div>
							            		
							            		<div class="jteHPlistingContentListDesc">
							            			<p class="">${response.data[i].post_excerpt}</p>
							            		</div>
							            	</div>
							            </a>`;

				        		$('.jteHospitalListingContent').append(divhtml);
							}

							
		                } else {
		                	console.log('response == ');
		                	$('.jteHAlistingContent').append('<div class="jteHospitalListingContentNoPost"><p>No posts found :(</p></div>');
		                	button.remove();
		                }
		            }
		        });

		        
		    });


		    function getURLParameters() {
			  	var search = window.location.search.substring(1); // Get the query string without the '?'
			  	var params = search.split('&');
			  	var paramObj = {};

			  	for (var i = 0; i < params.length; i++) {
				    var keyValue = params[i].split('=');
				    var key = decodeURIComponent(keyValue[0]);
				    var value = decodeURIComponent(keyValue[1]);
				    paramObj[key] = value;
			  	}

			  	return paramObj;
			}

			var params = getURLParameters();
			var paramSearch = 'jsearch';
			var paramTopic = 'jhospital';
			var paramSpecialties = 'jspecialty';

	  		console.log('params: ', params);

	  		function checkHospital() {
	  			const currentHospital = $('#jhospital-list').val();
	  			const klang = 15136;
	  			const tebrau = 616;
	  			const bukit_rimau = 645;
	  			console.log('currentHospital: ', currentHospital);

	  			if(currentHospital == 'all'){
	  				$('.jteSpecialtiesListingFooterOuter').show();
	  				$('.dialysisCentre').show();
	  				$('.cancerCentre').show();
	  			} else if(currentHospital == bukit_rimau){
	  				$('.jteSpecialtiesListingFooterOuter').show();
	  				$('.dialysisCentre').hide();
	  				$('.cancerCentre').show();
	  			} else if(currentHospital == klang || currentHospital == tebrau){
	  				$('.jteSpecialtiesListingFooterOuter').show();
	  				$('.cancerCentre').hide();
	  				$('.dialysisCentre').show();
	  			} else {
	  				$('.jteSpecialtiesListingFooterOuter').hide();
	  			}
	  		}

	  		// console.log('currentHospital: ', $('#jhospital-list').val());

	  		// if(paramSearch != undefined || paramTopic in params || paramSpecialties in params){
	  		// 	console.log('HAVE VALUE!', paramSearch != undefined, paramTopic in params != '', paramSpecialties in params != '');
	  		// } else {
	  		// 	console.log('NO VALUE!');
	  		// }

	  		// if (Object.values(params).some(value => value !== undefined)) {
			//     console.log('NOT undefined 01');
			// } else {
			// 	console.log('undefined 01');
			// }

			if (params[""] !== undefined) {
			    console.log('NOT undefined 02');
			} else {
				console.log('undefined 02');
				$('html, body').animate({
		           'scrollTop':   $('.jteSpecialtyListingContentContainer').offset().top - 120
		         }, 'slow');
			}

	  		if(paramSearch != ''){
	  			console.log('params[paramSearch]: ', params[paramSearch]);
	  			$('#searchBox').val(params[paramSearch]);
	  		} else {
	  			$('#searchBox').val('');
	  		}

	  		if(paramTopic in params){
	  			console.log('params[paramTopic]: ', params[paramTopic]);
	  			if(params[paramTopic] != ''){
	  				console.log('Have Topic: ', paramTopic);
	  				$("#jhospital-list").val(params[paramTopic]).selectmenu("refresh");
	  			} else {
	  				console.log('Empty Topic');
	  				$("#jhospital-list").val('all').selectmenu("refresh");
	  			}
	  			checkHospital();
	  		} else {
	  			console.log('params[paramTopic] haha: ');
	  			$("#jhospital-list").val('all').selectmenu("refresh");
	  		}

	  		if(paramSpecialties in params){
	  			console.log('params[paramSpecialties]: ', params[paramSpecialties]);
	  			if(params[paramSpecialties] != ''){
	  				$("#jspecialty-list").val(params[paramSpecialties]).selectmenu("refresh");
	  			} else {
	  				$("#jspecialty-list").val('all').selectmenu("refresh");
	  			}
	  			
	  		} else {
	  			$("#jspecialty-list").val('all').selectmenu("refresh");
	  		}


	    };


    });

})(jQuery);