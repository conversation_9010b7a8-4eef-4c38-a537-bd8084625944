<?php
// Include Jupiter X.
require_once( get_template_directory() . '/lib/init.php' );

// acf_form_head();

get_header();
?>

<?php
get_sidebar();
// get_footer();
?>

<style type="text/css">
.jupiterx-main-header {
	display: none !important;
}

.jupiterx-main-content {
	padding: 0px;
}

.jupiterx-main > .jupiterx-main-content > .container {
	max-width: 100% !important;
}
</style>

<!-- <link rel='stylesheet' id='jte-style-css' href='/malaysia/wp-content/plugins/elementor-addon/css/jte.css' type='text/css' media='all' /> -->


<div class="jteDoctorlandingContainer jteGeneralHorizontal">
	<div class="jteDoctorlandingContainerInner">
		<div class="jteDoctorLandingTitle">
			<h2>Doctor's <b>Profile</b></h2>
		</div>

		<?php 
			if (has_post_thumbnail()) {
			    $thumbnail_id = get_post_thumbnail_id();
			    $image_url = wp_get_attachment_image_src($thumbnail_id, 'full'); // 'full' is the image size
			    $image_url = $image_url[0];
			}

			// Doctor Name Change Case
			// function customTitleCase($string) {
			//     // Words that should be fully uppercase
			//     $uppercase_words = ['A/P', 'A/L'];

			//     // Split the string into words
			//     $words = explode(' ', $string);

			//     // Iterate through each word and convert accordingly
			//     foreach ($words as &$word) {
			//         // Check if the word should be fully uppercase
			//         if (in_array(strtoupper($word), $uppercase_words)) {
			//             $word = strtoupper($word);
			//         } else {
			//             // Convert to title case
			//             $word = ucfirst(strtolower($word));
			//         }
			//     }

			//     // Join the words back into a single string
			//     return implode(' ', $words);
			// }

			$doctor_name = get_the_title();
			$doctor_name_final = ucwords(strtolower($doctor_name));
			$doctor_id = get_the_ID();

			$doctor_name_final02 = customTitleCaseDoctor($doctor_name);

			// print_r($doctor_name_final);

			// $words = explode(' ', $doctor_name_final);
			// $doctor_result = implode(' ', array_slice($words, 0, 2));

			if (preg_match("/dr\.\s+\w+/i", $doctor_name_final, $matches)) {
		        // echo $matches[0] . "\n";
		        $doctor_result = $matches[0];
		    }
		    

			// print_r($words);
			// print_r($doctor_result);


			$specialty = get_field("specialty_api");
			$specialty_final = ucwords(strtolower($specialty));

			$qualification_title = get_field("qualification_title");

			$language = get_field("language");

			$designation = get_field("designation");

			$location = get_field("hospital");
			// print_r(count($location));
			// //print_r($location);
			// print_r($location->ID);
			// print_r($location->post_title);
			// if(isset($location)){
			// 	// print_r($location[0]);
			// 	$location_final = $location[0]->post_title;
            // }

			$appointment_arr = array();
            $jdoctorid = get_field('doctorid');
            $jdoctorname = $doctor_name;
            $jhospital_id = 0;
            $jhospital_name = "";
            $location = get_field("hospital");
            $titles = [];

            // print_r(get_the_title($location->ID));
            // print_r(implode(', ', $titles));

            // new code check data
            if (is_array($location)) {
			    // It's an array (could be a repeater, relationship, etc.)
			    foreach ($location as $post_object) {
			    	$titles[] = get_the_title($post_object->ID);
			    	// $jhospital_id = get_the_excerpt($post_object->ID);
                    // $jhospital_name = get_the_title($post_object->ID);

                    // $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".$jdoctorname."&location=".$jhospital_id."&searchLocation=".$jhospital_name."";
			        // // Process each item in the array
			        // if (is_object($item) && $item instanceof WP_Post) {
			        //     // It's a post object within the array
			        // } else {
			        //     // It's a simple data type within the array
			        // }
			    }
			} elseif (is_object($location) && $location instanceof WP_Post) {
				$titles[] = get_the_title($location->ID);
				// $jhospital_id = get_the_excerpt($post_object->ID);
	            //     $jhospital_name = get_the_title($location->ID);

	            //     $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".$jdoctorname."&location=".$jhospital_id."&searchLocation=".$jhospital_name."";
			    // It's a post object
			} else {
				$titles[] = get_the_title($location->ID);
				// $jhospital_id = get_the_excerpt($post_object->ID);
	            //     $jhospital_name = get_the_title($location->ID);

	            //     $appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".$jdoctorname."&location=".$jhospital_id."&searchLocation=".$jhospital_name."";
			    // It's a simple data type
			}

			
			// Old code check data
            // if ($location) {
	        //     // $titles = [];

	        //     foreach ($location as $post_object) {
	        //         $titles[] = get_the_title($post_object->ID);
	        //     }

	        //     // Output titles as a comma-separated string
	        //     // echo implode(', ', $titles);
	        // }

	        // print_r(implode(', ', $titles));

            $specialty2 = get_field("specialtydetails");
            $specialty3 = get_field("specialty");
            // print_r($specialty2);
            // print_r('<br/>');
            // print_r($specialty);
            // print_r('<br/>');
            // print_r($specialty_final);
            // print_r('<br/>');
            // print_r('<br/>');
            $specialty_final = explode(', ', $specialty);
			$specialty3_array = array_map('strtolower', explode(', ', $specialty2));
            // print_r( $specialty_final);

            //print_r($titles);
            // print_r('<br/>');
            // print_r($location);

            //start latest code check booking
            $tempappointment = get_field("appointment_list");
			$commaSeparated = explode(", ", $tempappointment);

			$resultArray = array();
			if(!empty($tempappointment)){
				foreach ($commaSeparated as $element) {
					if (strpos($element, '-') !== false) {
						list($jhospital_id, $status) = explode("-", $element);
					    $temphospitalname = "";
					    
					    // foreach ($location as $post_object) {
					    // 	//jtesttest = get_field()
					    // 	echo "<pre>" . $jhospital_id . " ||| " . $post_object->post_excerpt . "</pre>";
					    // 	if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
					    // 		$temphospitalname = $post_object->post_title;
					    // 	}
					    // }

					    if (is_array($location)) {
			                // It's an array (could be a repeater, relationship, etc.)
			                foreach ($location as $post_object) {
			                    if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
						    		$temphospitalname = $post_object->post_title;
						    	}
			                }
			            } elseif (is_object($location) && $location instanceof WP_Post) {
			                $jhospital_id = get_the_excerpt($post_object->ID);
			                $jhospital_name = get_the_title($location->ID);

			                if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
					    		$temphospitalname = $post_object->post_title;
					    	}
			                // It's a post object
			            } else {
			                $jhospital_id = get_the_excerpt($post_object->ID);
			                $jhospital_name = get_the_title($location->ID);

			                if( (int)trim($jhospital_id) == (int)trim($post_object->post_excerpt)){
					    		$temphospitalname = $post_object->post_title;
					    	}
			                // It's a simple data type
			            }

					    $resultArray[] = array("hospitalid" => $jhospital_id, "status" => $status, "hospitalname" => $temphospitalname);

					    // if($status == 2){
					    if($status == 2 && $temphospitalname != ""){
					    	// Check if hospital is Batu Kawan for special URL
					    	if($temphospitalname == "Batu Kawan") {
					    		$appointment_arr[] = "https://www.columbiaasia.com/malaysia/hospitals/batu-kawan#batu-kawan-maa";
					    	} else {
					    	$appointment_arr[] = "https://malaysiaportal.columbiaasia.com/webconnect/#/bookAppointment/%3FphyID=".$jdoctorid."&searchPhyAndSpec=".str_replace('/', '%252F', $jdoctorname)."&location=".$jhospital_id."&searchLocation=".$temphospitalname."";
					    }
					}
					}
				    
				}
			}
			//end latest code check booking
		?>
		<div class="jteDoctorLandingContent">
			<div class="jteDoctorLandingContentLeft">
				<div class="jteDoctorLandingContentLeftProfile" style="background-image: url(<?php echo esc_url($image_url); ?> );"></div>
				<!-- <div class="jteDoctorLandingContentLeftBtn">
					<a href="">Make an Appointment</a>
				</div> -->
			</div>
			<div class="jteDoctorLandingContentRight">
				<div class="jteDoctorLandingContentRightName">
					<h2><?php 
						// echo $doctor_name_final; 
						echo $doctor_name_final02; 
					?></h2>
					<p><?php echo $designation; ?></p>
				</div>
				<div class="jteDoctorLandingContentLeftBtn jteDoctorLandingContentLeftBtnM">
					<!-- <a href="javascript:void(0)" data-href="<?=implode("|||", $appointment_arr)?>" class="doctorPopup">Make an Appointment</a> -->
					<?php 
						if (strpos(implode("|||", $appointment_arr), "|||") !== false) {
						    // echo "Found ||| in the string. Do something.";
			          		echo'<a href="javascript:void(0)" data-href="'. implode("|||", $appointment_arr).'" class="doctorPopup">Make An Appointment</a>';
						} else {
						    // echo "||| not found in the string.";
						    $appointment_str = implode("|||", $appointment_arr);
						    // Check if this is a Batu Kawan URL
						    if (strpos($appointment_str, 'batu-kawan') !== false) {
						    	echo'<a href="'. $appointment_str .'" class="raven-button appointment-btn" data-doctor="' . esc_attr($jdoctorname) . '" title="Doctor: ' . esc_attr($jdoctorname) . '">Make An Appointment</a>';
						    } else {
						    	echo'<a href="'. $appointment_str .'" target="_blank">Make An Appointment</a>';
						    }
						}
					?>
				</div>
				<div class="jteDoctorLandingContentRightContent">
					<div class="jteDoctorLandingContentRightContentList doctorContent01">
						<div><p>Credentials</p></div>
						<div class="doctorContent01Inner"><p><?php echo $qualification_title; ?></p></div>
					</div>
					<div class="jteDoctorLandingContentRightContentList doctorContent02">
						<div><p>Languages</p></div>
						<div class="doctorContent02Inner"><p><?php echo $language; ?></p></div>
					</div>
					<!-- <div class="jteDoctorLandingContentRightContentList doctorContent03">
						<div><p>Locations</p></div>
						<div class="doctorContent03Inner"><p></p></div>
					</div> -->

					<div class="jteDoctorLandingContentRightContentList doctorContent03">
						<div><p>Locations</p></div>
						<div class="doctorContent03Inner">
							<p>
								<?php 
								if (is_array($location)) {
								    // It's an array (could be a repeater, relationship, etc.)
								    $index = 0;
	                				$count = count($location);
								    foreach ($location as $post2) {
								    	if ($index < $count - 1) {
									    	echo '<a href="'. get_post_permalink($post2->ID) .'">';
			                					echo ''. get_the_title($post2->ID) .', ';
			                				echo '</a>';
			                			}else{
			                				echo '<a href="'. get_post_permalink($post2->ID) .'">';
			                					echo ''. get_the_title($post2->ID);
			                				echo '</a>';
			                			}
			                			$index++;
								    }
								} elseif (is_object($location) && $location instanceof WP_Post) {
									echo '<a href="'. get_post_permalink($hospital->ID) .'">';
	                					echo get_the_title($hospital->ID);
	                				echo '</a>';
								    // It's a post object
								} else {
									echo '<a href="'. get_post_permalink($location->ID) .'">';
	                					echo get_the_title($location->ID);
	                				echo '</a>';
								    // It's a simple data type
								}
								?>
							</p>
						</div>
					</div>
					
					<div class="jteDoctorLandingContentRightContentList doctorContent04">
						<div><p>Clinic Hours</p></div>
						<div class="jteDoctorLandingContentRightClinicalHour doctorContent04Inner">
							<?php
								the_content();
							?>
						</div>
					</div>

					<div class="jteDoctorLandingContentRightContentList doctorContent05">
						<div><p>Specialties</p></div>
						<div class="doctorContent05Inner">
							<?php 
								// echo $specialty; 
								foreach ($specialty_final as $specialties) {
								// Convert the current specialty to lowercase for comparison
								$specialties_lower = strtolower($specialties);
								// $specialty3_array = explode(', ', strtolower($specialty2));
								$url_friendly_specialty = strtolower(str_replace(' ', '-', $specialties));
								
								// Handle the "General Practice" case separately
								if (strtolower($specialties) === "general practice" || strtolower($specialties) === "rehabilitation physician") {
									echo '<p class="jteDoctorLandingContentRightSpecialty">';
									echo $specialties;
									echo '</p>';
									continue; 
								}
								
								// Check if the lowercase specialty is in the $specialty3_array
								if (in_array($specialties_lower, $specialty3_array)) {
									// If it matches, display with <a> tag
									echo '<a href="/malaysia/specialties/' . $url_friendly_specialty . '" class="jteDoctorLandingContentRightSpecialty">';
									echo $specialties;
									echo '</a>';
								} else {
									// If it doesn't match, display only the text
									echo '<p class="jteDoctorLandingContentRightSpecialty">';
									echo $specialties;
									echo '</p>';
								}
								}
							?>
							
						</div>
					</div>
				</div>

				<div class="jteDoctorLandingContentLeftBtn">
					<!-- <a href="javascript:void(0)" data-href="<?=implode("|||", $appointment_arr)?>" class="doctorPopup">Make an Appointment</a> -->
					<?php 
						// if (strpos(implode("|||", $appointment_arr), "|||") !== false) {
						//     // echo "Found ||| in the string. Do something.";
			   //        		echo'<a href="javascript:void(0)" data-href="'. implode("|||", $appointment_arr).'" class="doctorPopup">Make An Appointment</a>';
						// } else {
						//     // echo "||| not found in the string.";
			   //        		echo'<a href="'.implode("|||", $appointment_arr).'" target="_blank">Make An Appointment</a>';
						// }

						if (strpos(implode("|||", $appointment_arr), "|||") !== false) {
						    // echo "Found ||| in the string. Do something.";
						    echo'<a href="javascript:void(0)" data-href="'. implode("|||", $appointment_arr).'" class="doctorPopup">Make An Appointment</a>';
						} else {
							if(count($appointment_arr) != 0){
								$appointment_str = implode("|||", $appointment_arr);
								// Check if this is a Batu Kawan URL
								if (strpos($appointment_str, 'batu-kawan') !== false) {
									echo'<a href="'. $appointment_str .'" class="raven-button appointment-btn" data-doctor="' . esc_attr($jdoctorname) . '" title="Doctor: ' . esc_attr($jdoctorname) . '">Make An Appointment</a>';
								} else {
									echo'<a href="'. $appointment_str .'" target="_blank">Make An Appointment</a>';
								}
							}else{
								
				          		// echo'<a href="'.implode("|||", $appointment_arr).'" target="_blank">No Appointment</a>';
				          		
							}
						    // echo "||| not found in the string.";
						    
						}
					?>
				</div>
				
			</div>
		</div>
	</div>
</div>


<?php 
	// print_r($doctor_id);
  	$meta_query_doctor[] =  array(
  		'key' => 'doctor',
  		// 'value' => $doctor_id,
  		'value' => $doctor_id,
  		'compare' => 'LIKE'
  	);

  	$meta_query[] = $meta_query_doctor;

	// $customPost = new WP_Query(array(
    //     'post_type' => 'health-article',
    //     'posts_per_page' => 4,
    //     'orderby' => 'date',
    //     'order' => 'DESC',
    //     'post_status' => 'publish',
    //     // 'meta_query' => $meta_query,
    //     // 'post__in' => $selected_items,
    //     // 'post__in' => array(157,153,152),
    // ));

    $haListingArgs = array(
        'post_type' => 'health-article',
        'orderby' => 'date',
        'order' => 'DESC',
        'posts_per_page' => 4,
        'post_status' => array('publish'),
        'meta_query' => $meta_query,
    );

	$haListingQuery = new WP_Query($haListingArgs);

    // print_r($customPost);
    // Temp Remark
	// if($haListingQuery->have_posts()){
	// 	echo '<div class="jteDoctorLandingHA">';
	// 		echo '<div class="jteHospitalListingNews jteHAGridContainer">';
	// 			echo '<div class="jteHospitalListingNewsInner">';
	// 				echo '<div class="jteHospitalListingNewsTitle"><h2>Health <b>Articles</b> by '. $doctor_result .'</h2></div>';
	// 				echo '<div class="jteHAGridHorizontalWrap owl-carousel">';
	// 				// echo '<div class="jteHAGridHorizontal owl-carousel">';

	// 				while ($haListingQuery->have_posts()) {
	// 	                $haListingQuery->the_post();
	// 	                // print_r($customPost);
	// 	                // print_r(get_the_ID($haListingQuery->ID));
	// 	                // $title = get_field("title");

	// 	                // $post = get_post($customPost->ID);
	// 	                // $post_slug = $post->post_name;
	// 	                // print_r($post_slug);
	// 	                $taxonomy_terms = wp_get_post_terms(get_the_ID(), 'topic', array('fields' => 'all'));
	// 		            $tax_topic = '';
	// 		            if (!empty($taxonomy_terms)) {
	// 			            foreach ($taxonomy_terms as $term) {
	// 			                // Access custom taxonomy data
	// 			                $term_id = $term->term_id;
	// 			                $tax_topic = $term->name;
	// 			                $term_slug = $term->slug;

	// 			                // Output or work with the custom taxonomy data
	// 			                // echo "Term ID: $term_id, Name: $term_name, Slug: $term_slug<br>";
	// 			            }
	// 			        }

	// 					$reading_time = get_field("reading_time_mins");


	// 	                // print_r($customPost->post_title);
	// 	                // '. get_the_ID() .'
	// 	                echo '<a href="'. get_post_permalink($haListingQuery->ID) .'" class="jteNewGridHorizontalList">';
	// 	                	echo '<div class="jteNewGridHorizontalListImg" style="background-image: url('. wp_get_attachment_image_src( get_post_thumbnail_id(), 'full' )[0] .')"></div>';
	// 	                	echo '<div class="jteNewGridHorizontalListContent">';
	// 	                		echo '<p class="jteHAGridTopic">'. $tax_topic .'</p>';
	// 	                		echo '<h3 class="jteNewGridWrapListTitle">'. wp_trim_words(get_the_title($haListingQuery->ID), 5, '...') . '</h3>';
	// 	                		echo '<div class="jteNewGridHorizontalListContentFooter">';
	// 		                		echo '<p class="jteNewGridHorizontalListDate">'. get_the_date('d F Y') .'</p>';
	// 		                		echo '<p class="jteNewGridHorizontalListReading"><i></i>'. $reading_time . ' min</p>';
	// 	                		echo '</div>';
	// 	                	echo '</div>';
	// 	                echo '</a>';
	// 	            }
	// 	            wp_reset_postdata();

	// 	            echo '</div>';
	// 	            echo '<div class="jteHospitalListingNewsBtn-m"><a href="/malaysia/health-article/">View All</a></div>';
	// 	        echo '</div>';
	// 		echo '</div>';
	// 	echo '</div>';
	// }


	// NEW CODE
	if($haListingQuery->have_posts()){
		echo '<div class="jteDoctorLandingHA">';
			echo '<div class="jteHospitalListingNews jteHAGridContainer">';
				echo '<div class="jteHospitalListingNewsInner">';
					echo '<div class="jteHospitalListingNewsTitle"><h2>Health <b>Articles</b> by '. $doctor_result .'</h2></div>';

					// echo '<div class="jteHospitalListingNewsTitle"><h2>Health <b>Articles</b> by '. $doctor_name_final .'</h2></div>';
					
					echo '<div class="jteHAGridHorizontalWrap owl-carousel">';
					// echo '<div class="jteHAGridHorizontal owl-carousel">';


					$cache_key = 'custom_doctor_inner_article--' . md5(json_encode($haListingArgs));
				    $posts_data = wp_cache_get($cache_key, 'jte_addon_cache');

				    if (!$posts_data) {
				    	// echo 'no cache';
				    	ob_start();
						while ($haListingQuery->have_posts()) {
			                $haListingQuery->the_post();

					    	$taxonomy_terms = wp_get_post_terms(get_the_ID(), 'topic', array('fields' => 'all'));
				            $tax_topic = '';
				            if (!empty($taxonomy_terms)) {
					            foreach ($taxonomy_terms as $term) {
					                // Access custom taxonomy data
					                $term_id = $term->term_id;
					                $tax_topic = $term->name;
					                $term_slug = $term->slug;

					                // Output or work with the custom taxonomy data
					                // echo "Term ID: $term_id, Name: $term_name, Slug: $term_slug<br>";
					            }
					        }

							$reading_time = get_field("reading_time_mins");

			                echo '<a href="'. get_post_permalink($haListingQuery->ID) .'" class="jteNewGridHorizontalList">';
			                	echo '<div class="jteNewGridHorizontalListImg" style="background-image: url('. wp_get_attachment_image_src( get_post_thumbnail_id(), 'full' )[0] .')"></div>';
			                	echo '<div class="jteNewGridHorizontalListContent">';
			                		echo '<p class="jteHAGridTopic">'. $tax_topic .'</p>';
			                		echo '<h3 class="jteNewGridWrapListTitle">'. wp_trim_words(get_the_title($haListingQuery->ID), 5, '...') . '</h3>';
			                		echo '<div class="jteNewGridHorizontalListContentFooter">';
				                		echo '<p class="jteNewGridHorizontalListDate">'. get_the_date('d F Y') .'</p>';
				                		echo '<p class="jteNewGridHorizontalListReading"><i></i>'. $reading_time . ' min</p>';
			                		echo '</div>';
			                	echo '</div>';
			                echo '</a>';
			                
			            }
			            wp_reset_postdata();

			            $posts_data = ob_get_clean();
        				wp_cache_set($cache_key, $posts_data, 'jte_addon_cache', 3600);
				    } else {
				    	// echo 'hv cache';
				    }

		            echo $posts_data;

		            echo '</div>';
		            echo '<div class="jteHospitalListingNewsBtn-m"><a href="/malaysia/health-article/">View All</a></div>';
		        echo '</div>';
			echo '</div>';
		echo '</div>';
	}
    

?>


<?php 
	// print_r($doctor_id);
  	$meta_query_doctor[] =  array(
  		'key' => 'doctor',
  		// 'value' => $doctor_id,
  		'value' => $doctor_id,
  		'compare' => 'LIKE'
  	);

  	$meta_query[] = $meta_query_doctor;

    $videoListingArgs = array(
        'post_type' => 'video-gallery',
        'orderby' => 'date',
        'order' => 'DESC',
        'posts_per_page' => 3,
        'post_status' => array('publish'),
        'meta_query' => $meta_query,
    );

	$videoListingQuery = new WP_Query($videoListingArgs);

    // print_r($customPost);
    // Temp Remark
	// if($videoListingQuery->have_posts()){
	// 	echo '<div class="jteDoctorLandingHA jteDoctorLandingVideo">';
	// 		echo '<div class="jteHospitalListingNews jteHAGridContainer jteDoctorLandingVideoContainer">';
	// 			echo '<div class="jteHospitalListingNewsInner">';
	// 				echo '<div class="jteHospitalListingNewsTitle"><h2><b>Vidoes</b> by '. $doctor_result .'</h2></div>';
	// 				// echo '<div class="jteHospitalListingNewsTitle"><h2><b>Vidoes</b> by '. $doctor_name_final .'</h2></div>';
					
	// 				echo '<div class="jteVideoGridHorizontal owl-carousel">';

	// 				while ($videoListingQuery->have_posts()) {
	// 	                $videoListingQuery->the_post();
	// 	                // print_r($customPost);
	// 	                // print_r(get_the_ID($haListingQuery->ID));
	// 	                // $title = get_field("title");

	// 	                // $post = get_post($customPost->ID);
	// 	                // $post_slug = $post->post_name;
	// 	                // print_r($post_slug);

	// 	                $taxonomy_terms = get_the_terms(get_the_ID(), 'video_type');
	// 		            if ($taxonomy_terms && !is_wp_error($taxonomy_terms)) {
	// 			            // Loop through the terms and do something with each term
	// 			            foreach ($taxonomy_terms as $term) {
	// 			                $term_name = $term->name; // Get the term name
	// 			                $term_slug = $term->slug; // Get the term slug
	// 			                $term_id = $term->term_id; // Get the term ID
	// 			                // Do something with the term information
	// 			            }
	// 			        }

	// 			        $embedLink = get_field('youtube_embed_link');


	// 	                // print_r($customPost->post_title);
	// 	                // '. get_the_ID() .'
	// 	                // echo '<a href="'. get_post_permalink($videoListingQuery->ID) .'" class="jteNewGridHorizontalList">';
	// 	                // 	echo '<div class="jteNewGridHorizontalListImg" style="background-image: url('. wp_get_attachment_image_src( get_post_thumbnail_id(), 'full' )[0] .')"></div>';
	// 	                // 	echo '<div class="jteNewGridHorizontalListContent">';
	// 	                // 		echo '<p class="jteHAGridTopic">'. $tax_topic .'</p>';
	// 	                // 		echo '<h3 class="jteNewGridWrapListTitle">'. wp_trim_words(get_the_title($videoListingQuery->ID), 5, '...') . '</h3>';
	// 	                // 		echo '<div class="jteNewGridHorizontalListContentFooter">';
	// 		            //     		echo '<p class="jteNewGridHorizontalListDate">'. get_the_date('d F Y') .'</p>';
	// 	                // 		echo '</div>';
	// 	                // 	echo '</div>';
	// 	                // echo '</a>';

	// 	                echo "<a href='javascript:void(0)'' class='jteHAlistingContentList' data-yt='". $embedLink . "'>";
	// 		            	echo '<div class="jteHAlistingContentListImg jteVideolistingContentImg" style="background-image:url('. wp_get_attachment_image_src( get_post_thumbnail_id(), 'full' )[0] .')">';
	// 						if($term_slug == 'video'){
	// 							echo '<div class="jteVideolistingContentBG"><i class="video"></i></div>';
	// 						} else if($term_slug == 'podcast'){
	// 							echo '<div class="jteVideolistingContentBG"><i class="podcast"></i></div>';
	// 						}
	// 		            	echo '</div>';
	// 		            	echo '<div class="jteHAlistingContentListContent">';
	// 		            		echo '<div class="jteHAlistingContentListTitle">';
	// 	                			echo '<h3 class="jteNewGridWrapListTitle">'. get_the_title($videoListingQuery->ID) . '</h3>';
	// 		            		echo '</div>';
	// 		            		echo '<div class="jteHAlistingContentListFooter">';
	// 		            			echo '<p class="jteHAlistingContentListDate">'. get_the_date('d F Y') .'</p>';
	// 		            		echo '</div>';
	// 		            	echo '</div>';
	// 		            echo '</a>';
	// 	            }

	// 	            wp_reset_postdata();

	// 	            echo '</div>';
	// 	            echo '<div class="jteHospitalListingNewsBtn-m"><a href="/malaysia/vide-gallery/">View All</a></div>';
	// 	        echo '</div>';
	// 		echo '</div>';
	// 	echo '</div>';
	// }
    
    // NEW CODE
    if($videoListingQuery->have_posts()){
		echo '<div class="jteDoctorLandingHA jteDoctorLandingVideo">';
			echo '<div class="jteHospitalListingNews jteHAGridContainer jteDoctorLandingVideoContainer">';
				echo '<div class="jteHospitalListingNewsInner">';
					echo '<div class="jteHospitalListingNewsTitle"><h2><b>Vidoes</b> by '. $doctor_result .'</h2></div>';
					// echo '<div class="jteHospitalListingNewsTitle"><h2><b>Vidoes</b> by '. $doctor_name_final .'</h2></div>';
					
					echo '<div class="jteVideoGridHorizontal owl-carousel">';

					$cache_key = 'custom_doctor_inner_video--' . md5(json_encode($videoListingArgs));
				    $posts_data = wp_cache_get($cache_key, 'jte_addon_cache');
				    // $isCache = false;

				    if (!$posts_data) {
				    	// echo 'no cache';
				    	// $isCache = false;
				    	ob_start();

						while ($videoListingQuery->have_posts()) {
			                $videoListingQuery->the_post();

			                $taxonomy_terms = get_the_terms(get_the_ID(), 'video_type');
				            if ($taxonomy_terms && !is_wp_error($taxonomy_terms)) {
					            // Loop through the terms and do something with each term
					            foreach ($taxonomy_terms as $term) {
					                $term_name = $term->name; // Get the term name
					                $term_slug = $term->slug; // Get the term slug
					                $term_id = $term->term_id; // Get the term ID
					                // Do something with the term information
					            }
					        }

					        $embedLink = get_field('youtube_embed_link');

			                echo "<a href='javascript:void(0)'' class='jteHAlistingContentList' data-yt='". $embedLink . "'>";
				            	echo '<div class="jteHAlistingContentListImg jteVideolistingContentImg" style="background-image:url('. wp_get_attachment_image_src( get_post_thumbnail_id(), 'full' )[0] .')">';
								if($term_slug == 'video'){
									echo '<div class="jteVideolistingContentBG"><i class="video"></i></div>';
								} else if($term_slug == 'podcast'){
									echo '<div class="jteVideolistingContentBG"><i class="podcast"></i></div>';
								}
				            	echo '</div>';
				            	echo '<div class="jteHAlistingContentListContent">';
				            		echo '<div class="jteHAlistingContentListTitle">';
			                			echo '<h3 class="jteNewGridWrapListTitle">'. get_the_title($videoListingQuery->ID) . '</h3>';
				            		echo '</div>';
				            		echo '<div class="jteHAlistingContentListFooter">';
				            			echo '<p class="jteHAlistingContentListDate">'. get_the_date('d F Y') .'</p>';
				            		echo '</div>';
				            	echo '</div>';
				            echo '</a>';
			            }

			            wp_reset_postdata();

			            $posts_data = ob_get_clean();
        				wp_cache_set($cache_key, $posts_data, 'jte_addon_cache', 3600);
				    } else {
				    	// echo 'hv cache';
				    }

		            echo $posts_data;


		            echo '</div>';
		            echo '<div class="jteHospitalListingNewsBtn-m"><a href="/malaysia/vide-gallery/">View All</a></div>';
		        echo '</div>';
			echo '</div>';
		echo '</div>';
	}

?>


<!-- <script type='text/javascript' src='/malaysia/wp-content/plugins/elementor-addon/js/jte.js'></script> -->

<!-- <script type="text/javascript">
jQuery(document).ready(function($) {
	function toTitleCase(str) {
	    return str.replace(
	        /\w\S*/g,
	        function(txt) {
	            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
	        }
	    );
	}

    var currentTitle = $('title').text();
    var titleCased = toTitleCase(currentTitle);
    // console.log('Page title Before: ', currentTitle, titleCased);
    $('title').text(titleCased); // Set new title
    console.log('Page title: ', currentTitle, titleCased);
});
</script> -->


<script type="text/javascript">
jQuery(document).ready(function($) {
	function toTitleCase(str) {
	    str = str.replace(
	        /\w\S*/g,
	        function(txt) {
	            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
	        }
	    );

	    str = str.replace(/a\/l/gi, 'A/L').replace(/a\/p/gi, 'A/P');

	    return str;
	}

    var currentTitle = $('title').text();
    var titleCased = toTitleCase(currentTitle);
    // console.log('Page title Before: ', currentTitle, titleCased);
    $('title').text(titleCased); // Set new title
    console.log('Page title: ', currentTitle, titleCased);
});
</script>

<?php
get_footer();
?>